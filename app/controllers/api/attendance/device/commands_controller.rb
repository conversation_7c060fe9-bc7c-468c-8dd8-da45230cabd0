# frozen_string_literal: true

module Api
  module Attendance
    module Device
      class CommandsController < ApplicationController
      include Athar::Commons::Api::Concerns::Serializable
      include Athar::Commons::Api::Concerns::Paginatable
      include Athar::Commons::Api::Concerns::FilterableSortable

      before_action :authenticate_session!
      before_action :set_device
      before_action :authorize_read, only: [ :index, :history ]
      before_action :authorize_execute, only: [ :create ]

      api! "Lists available commands for a device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "ID of the attendance device"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Returns a list of commands available for the specified device.
        Commands vary by device type and adapter capabilities.
        Requires permission: <code>:read, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "List of available commands"
      error code: 404, desc: "Device not found"

      def index
        adapter = @device.create_adapter

        unless adapter.supports_commands?
          render json: {
            device_id: @device.id,
            device_name: @device.name,
            adapter_type: @device.adapter_type,
            supports_commands: false,
            available_commands: [],
            message: "This device does not support commands"
          }
          return
        end

        commands = build_command_definitions(adapter)

        render json: {
          device_id: @device.id,
          device_name: @device.name,
          adapter_type: @device.adapter_type,
          supports_commands: true,
          available_commands: commands,
          retrieved_at: Time.current.iso8601
        }
      end

      api! "Executes a command on a device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "ID of the attendance device"
      param :command, Hash, required: true, desc: "Command execution parameters" do
        param :name, String, required: true, desc: "Command name to execute"
        param :parameters, Hash, desc: "Command-specific parameters"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Executes a command on the specified device.
        Commands are executed synchronously and return immediate results.
        All command executions are logged for audit purposes.
        Requires permission: <code>:execute_commands, :attendance_device</code>.
      HTML
      )
      returns code: 200, desc: "Command execution result"
      error code: 400, desc: "Invalid command or parameters"
      error code: 404, desc: "Device not found"

      def create
        command_data = command_params

        # Validate command
        validation = ::Attendance::CommandExecutor.validate_command(
          @device,
          command_data[:name],
          command_data[:parameters]
        )

        unless validation[:valid]
          serialize_errors({ detail: validation[:error] }, :bad_request)
          return
        end

        # Execute command
        result = ::Attendance::CommandExecutor.execute(
          @device,
          command_data[:name],
          command_data[:parameters],
          current_employee
        )

        # For plain hash responses, use render json directly
        render json: {
          device_id: @device.id,
          command_name: command_data[:name],
          parameters: command_data[:parameters],
          result: result.as_json,
          executed_at: Time.current.iso8601,
          executed_by: current_employee&.id
        }, status: :created
      end

      api! "Gets command execution history for a device"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :device_id, Integer, required: true, desc: "ID of the attendance device"
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param_group :include_params
      param_group :fields_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Returns the command execution history for the specified device.
        Supports filtering, sorting, and pagination following JSON:API standards.
        Requires permission: <code>:read, :attendance_device</code>.

        <b>Filtering Examples:</b>
        <code>GET /api/attendance/devices/1/commands/history?filter[command_name]=test_voice</code>
        <code>GET /api/attendance/devices/1/commands/history?filter[status]=completed</code>

        <b>Sorting Examples:</b>
        <code>GET /api/attendance/devices/1/commands/history?sort=-started_at</code>
        <code>GET /api/attendance/devices/1/commands/history?sort=command_name,started_at</code>
      HTML
      )
      returns code: 200, desc: "Command execution history"
      error code: 404, desc: "Device not found"

      def history
        # Set up collection for filtering and sorting
        @collection = @device.command_executions.includes(:executed_by, :device)

        apply_filters(@collection) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)

          # Add device context to meta
          meta[:device] = {
            id: @device.id,
            name: @device.name,
            adapter_type: @device.adapter_type
          }

          serialize_response(records, serializer: ::Attendance::CommandExecutionSerializer, meta: meta)
        end
      end

      private

      def set_device
        @device = ::Attendance::Device.find(params[:device_id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Attendance device not found" }, :not_found)
      end

      def command_params
        command_data = params.require(:command).permit(:name, parameters: {})
        {
          name: command_data[:name],
          parameters: command_data[:parameters] || {}
        }
      end

      # Authorization methods following API standards
      def authorize_read
        unless can?(:read, :attendance_device)
          render_forbidden("You don't have permission to view device commands")
          false
        end
      end

      def authorize_execute
        unless can?(:execute_commands, :attendance_device)
          render_forbidden("You don't have permission to execute device commands")
          false
        end
      end

      def render_forbidden(message)
        serialize_errors({ detail: message }, :forbidden)
      end

      # Filtering and sorting configuration
      def filterable_fields
        %w[command_name status started_at completed_at]
      end

      def sortable_fields
        %w[command_name status started_at completed_at duration]
      end

      def default_sort
        '-started_at' # Most recent first
      end

      def searchable_fields
        %w[command_name]
      end

      def build_command_definitions(adapter)
        adapter.available_commands.map do |command_name|
          command_class = adapter.get_command_class(command_name)
          next unless command_class

          {
            name: command_name,
            display_name: command_class.command_name.titleize,
            description: command_class.description,
            parameters: extract_parameter_definitions(command_class)
          }
        end.compact
      end

      def extract_parameter_definitions(command_class)
        parameters = {}

        command_class.attribute_names.each do |attr_name|
          # Skip internal attributes
          next if %w[adapter_type device_id].include?(attr_name)

          attribute_definition = command_class.attribute_types[attr_name]
          parameters[attr_name] = {
            type: attribute_definition.type,
            required: command_required_parameter?(command_class, attr_name),
            default: extract_default_value(command_class, attr_name),
            description: extract_parameter_description_from_model(command_class, attr_name)
          }
        end

        parameters
      end

      def command_required_parameter?(command_class, attr_name)
        command_class.required_parameters.include?(attr_name)
      end

      def extract_default_value(command_class, attr_name)
        # Try to get default from ActiveStruct attribute definition
        begin
          # Create a temporary instance to get the default value
          temp_instance = command_class.new
          default_value = temp_instance.send(attr_name)

          # Only return non-nil defaults or explicit false values
          return default_value if default_value != nil || default_value == false
        rescue
          # If we can't get the default, return nil
        end

        nil
      end

      def extract_parameter_description_from_model(command_class, attr_name)
        # Check if the command class defines parameter descriptions
        if command_class.respond_to?(:parameter_descriptions)
          descriptions = command_class.parameter_descriptions
          return descriptions[attr_name] if descriptions[attr_name]
        end

        # Check if the command class defines a specific method for this parameter
        description_method = "#{attr_name}_description"
        if command_class.respond_to?(description_method)
          return command_class.send(description_method)
        end

        # Fallback: generate description from attribute name and validations
        generate_parameter_description(command_class, attr_name)
      end

      def generate_parameter_description(command_class, attr_name)
        # Create a temporary instance to inspect validations
        temp_instance = command_class.new
        temp_instance.valid? # Trigger validations

        # Get validation details for this attribute
        validations = temp_instance.errors.details[attr_name.to_sym] || []

        # Generate description based on attribute name and validations
        base_description = attr_name.humanize.downcase

        # Add validation hints
        validation_hints = []
        validations.each do |validation|
          case validation[:error]
          when :inclusion
            if validation[:value]
              validation_hints << "must be one of: #{validation[:value].join(', ')}"
            end
          when :numericality
            if validation[:greater_than]
              validation_hints << "must be greater than #{validation[:greater_than]}"
            end
            if validation[:less_than_or_equal_to]
              validation_hints << "must be #{validation[:less_than_or_equal_to]} or less"
            end
          when :length
            if validation[:maximum]
              validation_hints << "maximum #{validation[:maximum]} characters"
            end
          end
        end

        description = base_description
        description += " (#{validation_hints.join(', ')})" if validation_hints.any?
        description.capitalize
      end
      end
    end
  end
end
