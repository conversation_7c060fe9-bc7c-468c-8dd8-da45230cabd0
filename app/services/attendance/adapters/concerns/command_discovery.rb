# frozen_string_literal: true

module Attendance
  module Adapters
    module Concerns
      module CommandDiscovery
        extend ActiveSupport::Concern
        
        def available_commands
          command_classes.keys
        end
        
        def get_command_class(command_name)
          command_classes[command_name.to_s]
        end
        
        def execute_command(command_name, parameters = {})
          command_class = get_command_class(command_name)
          return Attendance::CommandResult.failure("Unknown command: #{command_name}") unless command_class
          
          command = command_class.new(parameters)
          command.execute(self)
        end
        
        private
        
        def command_classes
          @command_classes ||= discover_commands
        end
        
        def discover_commands
          commands = {}
          adapter_name = self.class.name.demodulize.gsub('Adapter', '')
          command_namespace = "Attendance::Adapters::#{adapter_name}"
          
          begin
            namespace_module = command_namespace.constantize
            namespace_module.constants.each do |const_name|
              const = namespace_module.const_get(const_name)
              if const.is_a?(Class) && const < Attendance::Adapters::BaseCommand
                command_name = const.command_name
                commands[command_name] = const
              end
            end
          rescue NameError => e
            Rails.logger.warn("Could not discover commands for #{command_namespace}: #{e.message}")
          end
          
          commands
        end
      end
    end
  end
end
