# frozen_string_literal: true

module Attendance
  module Adapters
    module Zkteco
      class TestVoiceCommand < Attendance::Adapters::BaseCommand
        # Command parameters
        attribute :index, :integer

        validates :index, presence: { message: "Index parameter is required" }
        validates :index, numericality: {
          greater_than_or_equal_to: 0,
          less_than_or_equal_to: 255,
          message: "Index must be between 0 and 255"
        }

        def self.command_name
          'test_voice'
        end

        def self.description
          'Test the device speaker/voice system with a specific voice index'
        end

        def self.parameter_descriptions
          {
            'index' => 'Voice index number to test (0-255, where each index represents a different sound or voice prompt)'
          }
        end

        def self.required_parameters
          %w[index]
        end

        protected

        def perform_execution(adapter)
          adapter.with_connection do |zk_client|
            if zk_client.respond_to?(:test_voice)
              result = zk_client.test_voice(index)
              adapter.log_info("Voice test command sent successfully")
              Attendance::CommandResult.success("Voice test executed with index #{index}")
            else
              adapter.log_warn("Test voice command not supported by the current rbzk version")
              Attendance::CommandResult.failure("Operation not supported by the current device library")
            end
          end
        rescue => e
          adapter.log_error("Error testing voice", e)
          Attendance::CommandResult.failure(e.message)
        end
      end
    end
  end
end
