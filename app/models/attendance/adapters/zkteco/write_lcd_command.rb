# frozen_string_literal: true

module Attendance
  module Adapters
    module Zkteco
      class WriteLcdCommand < Attendance::Adapters::BaseCommand
        # Command parameters
        attribute :message, :string

        validates :message, presence: { message: "Message parameter is required" }
        validates :message, length: { maximum: 100, message: "Message must be 100 characters or less" }

        def self.command_name
          'write_lcd'
        end

        def self.description
          'Write a message to the ZKTeco device LCD display'
        end

        def self.parameter_descriptions
          {
            'message' => 'Text message to display on the LCD screen'
          }
        end

        def self.required_parameters
          %w[message]
        end

        protected

        def perform_execution(adapter)
          adapter.with_connection do |zk_client|
            if zk_client.respond_to?(:write_lcd)
              result = zk_client.write_lcd(message)
              adapter.log_info("Message written to <PERSON><PERSON> successfully")
              Attendance::CommandResult.success("Message written to LCD: #{message}")
            else
              adapter.log_warn("Write LCD command not supported by the current rbzk version")
              Attendance::CommandResult.failure("Operation not supported by the current device library")
            end
          end
        rescue => e
          adapter.log_error("Error writing to <PERSON><PERSON>", e)
          Attendance::CommandResult.failure(e.message)
        end
      end
    end
  end
end
